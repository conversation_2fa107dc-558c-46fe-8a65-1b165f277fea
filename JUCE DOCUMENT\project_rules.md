这是一个Juce架构的C++音乐混音插件项目，制作一个能够使用一个单个的旋钮来做到音频压缩功能辅助混音的插件，Trae的目的是帮助制作者做出这个插件并提醒制作者有什么方面可以进行补充制作和学习。用JUCE_PROJECT_INFO宏定义了项目的名称、版本号、公司名称等信息。
rules文件夹下有一些Juce官方的MD文档，请严格参考这些文档的要求进行Juce项目的编写。
对于复杂的建议，要求 AI “解释其逻辑”。
要求你一步步执行小而具体的任务，不要一次实现所有事
优先采用符合 JUCE 习惯用法（idiomatic）的解决方案。
时刻注意音频线程（Audio Thread）的限制。
要求使用juce模块来完成标准任务。例如：“使用 juce::dsp::Compressor 来实现核心压缩功能。” 避免让 AI 从头编写复杂的 DSP

## 开发计划

1.  **定义核心压缩逻辑**：明确单旋钮如何控制阈值(Threshold)、比率(Ratio)、启动时间(Attack)、释放时间(Release)以及补偿增益(Makeup Gain)等参数，并确定它们之间的联动关系。
2.  **实现参数管理**：使用 `juce::AudioProcessorValueTreeState` 来管理插件参数，确保参数可以被宿主自动化控制并保存/加载。
3.  **实现音频处理**：在 `processBlock` 函数中，根据旋钮值获取相应的压缩参数，并使用 `juce::dsp::Compressor` 类处理音频信号。
4.  **创建用户界面**：设计一个简洁的界面，包含一个主旋钮，并可能添加输入/输出/增益衰减表等视觉反馈元素。
5.  **连接UI与参数**：使用 `juce::AudioProcessorValueTreeState::SliderAttachment` 等附件类将UI控件与参数连接起来。
6.  **测试与优化**：在不同的宿主软件中测试插件，根据反馈调整参数联动曲线和压缩效果。
